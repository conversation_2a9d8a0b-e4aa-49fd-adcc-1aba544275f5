# Oracle SYS Password Manager

A secure Python script to retrieve and manage Oracle SYS user passwords from various sources.

## Features

- Multiple password retrieval methods:
  - Environment variables
  - Configuration files
  - Text files
  - Interactive prompts
- Secure Oracle SYS connection testing
- Query execution as SYS user
- Configurable database connection parameters
- Comprehensive logging

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Install Oracle Instant Client (if not already installed):
   - Download from Oracle website
   - Follow installation instructions for your OS

## Configuration

### Method 1: Environment Variable
```bash
export ORACLE_SYS_PASSWORD="your_sys_password"
```

### Method 2: Configuration File
1. Copy the template:
```bash
cp oracle_config.json.template oracle_config.json
```

2. Edit `oracle_config.json` with your settings:
```json
{
    "database": {
        "host": "your_oracle_host",
        "port": 1521,
        "service_name": "your_service_name"
    },
    "credentials": {
        "sys_password": "your_sys_password"
    }
}
```

### Method 3: Password File
Create a text file with the password:
```bash
echo "your_sys_password" > oracle_sys_password.txt
chmod 600 oracle_sys_password.txt
```

## Usage

### Test Connection
```bash
# Test with auto-detection of password method
python get_oracle_sys_password.py --test

# Test with specific method
python get_oracle_sys_password.py --test --method env

# Test with custom connection parameters
python get_oracle_sys_password.py --test --host myhost --port 1521 --service ORCL
```

### Execute Queries
```bash
# Execute a simple query
python get_oracle_sys_password.py --query "SELECT username FROM dba_users WHERE username='SYS'"

# Execute with specific connection parameters
python get_oracle_sys_password.py --query "SELECT * FROM v\$version" --host myhost --service ORCL
```

### Retrieve Password Only
```bash
# Get password using auto-detection
python get_oracle_sys_password.py

# Get password from specific source
python get_oracle_sys_password.py --method config
```

## Command Line Options

- `--method`: Password retrieval method (`env`, `file`, `config`, `interactive`, `auto`)
- `--host`: Oracle database host (default: localhost)
- `--port`: Oracle database port (default: 1521)
- `--service`: Oracle service name (default: XE)
- `--test`: Test connection only
- `--query`: Execute custom SQL query
- `--config`: Path to configuration file

## Security Best Practices

1. **Environment Variables**: Use for containerized environments
2. **Configuration Files**: Secure file permissions (600)
3. **Password Files**: Use restrictive permissions and consider encryption
4. **Interactive Mode**: Use for one-time operations
5. **Never hardcode passwords** in scripts

## Examples

### Basic Connection Test
```bash
python get_oracle_sys_password.py --test --host prod-oracle --service PROD
```

### Check Database Version
```bash
python get_oracle_sys_password.py --query "SELECT * FROM v\$version"
```

### List All Users
```bash
python get_oracle_sys_password.py --query "SELECT username, account_status FROM dba_users ORDER BY username"
```

### Check Tablespace Usage
```bash
python get_oracle_sys_password.py --query "SELECT tablespace_name, bytes/1024/1024 as MB FROM dba_data_files"
```

## Troubleshooting

1. **Oracle Client Issues**: Ensure Oracle Instant Client is properly installed
2. **Connection Errors**: Verify host, port, and service name
3. **Permission Errors**: Check file permissions for password files
4. **Driver Issues**: Try switching between `oracledb` and `cx_Oracle`

## Error Codes

- Exit code 0: Success
- Exit code 1: General error (connection failed, password not found, etc.)
