#!/bin/bash

# Oracle SYS Password Retrieval Script (Bash Version)
# This script provides methods to securely retrieve Oracle SYS passwords

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/oracle_config.json"
PASSWORD_FILE="${SCRIPT_DIR}/oracle_sys_password.txt"
LOG_FILE="${SCRIPT_DIR}/oracle_connection.log"

# Default values
DEFAULT_HOST="localhost"
DEFAULT_PORT="1521"
DEFAULT_SERVICE="XE"
DEFAULT_METHOD="auto"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Error handling
error() {
    log "ERROR" "$*" >&2
    exit 1
}

# Warning function
warn() {
    log "WARN" "$*" >&2
}

# Info function
info() {
    log "INFO" "$*"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command_exists sqlplus; then
        missing_deps+=("sqlplus (Oracle SQL*Plus)")
    fi
    
    if ! command_exists jq && [[ -f "$CONFIG_FILE" ]]; then
        missing_deps+=("jq (for JSON parsing)")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        error "Missing dependencies: ${missing_deps[*]}"
    fi
}

# Get password from environment variable
get_password_from_env() {
    local env_var="${1:-ORACLE_SYS_PASSWORD}"
    
    if [[ -n "${!env_var:-}" ]]; then
        info "Password retrieved from environment variable: $env_var"
        echo "${!env_var}"
        return 0
    else
        warn "Environment variable $env_var not found"
        return 1
    fi
}

# Get password from file
get_password_from_file() {
    local file_path="${1:-$PASSWORD_FILE}"
    
    if [[ -f "$file_path" ]]; then
        if [[ -r "$file_path" ]]; then
            info "Password retrieved from file: $file_path"
            cat "$file_path" | tr -d '\n\r'
            return 0
        else
            warn "Password file not readable: $file_path"
            return 1
        fi
    else
        warn "Password file not found: $file_path"
        return 1
    fi
}

# Get password from config file
get_password_from_config() {
    local config_file="${1:-$CONFIG_FILE}"
    local key="${2:-credentials.sys_password}"
    
    if [[ -f "$config_file" ]]; then
        if command_exists jq; then
            local password
            password=$(jq -r ".$key // empty" "$config_file" 2>/dev/null)
            if [[ -n "$password" && "$password" != "null" ]]; then
                info "Password retrieved from config file: $config_file"
                echo "$password"
                return 0
            else
                warn "Password key '$key' not found in config file"
                return 1
            fi
        else
            warn "jq not available for JSON parsing"
            return 1
        fi
    else
        warn "Config file not found: $config_file"
        return 1
    fi
}

# Get password interactively
get_password_interactive() {
    info "Prompting for password interactively"
    read -s -p "Enter Oracle SYS password: " password
    echo >&2  # New line after password input
    echo "$password"
}

# Get password using specified method
get_password() {
    local method="${1:-auto}"
    local password=""
    
    case "$method" in
        "env")
            password=$(get_password_from_env) || return 1
            ;;
        "file")
            password=$(get_password_from_file) || return 1
            ;;
        "config")
            password=$(get_password_from_config) || return 1
            ;;
        "interactive")
            password=$(get_password_interactive) || return 1
            ;;
        "auto")
            # Try methods in order of preference
            password=$(get_password_from_env 2>/dev/null) || \
            password=$(get_password_from_config 2>/dev/null) || \
            password=$(get_password_from_file 2>/dev/null) || \
            password=$(get_password_interactive) || return 1
            ;;
        *)
            error "Unknown password retrieval method: $method"
            ;;
    esac
    
    if [[ -n "$password" ]]; then
        echo "$password"
        return 0
    else
        error "Failed to retrieve password"
    fi
}

# Test Oracle connection
test_connection() {
    local host="${1:-$DEFAULT_HOST}"
    local port="${2:-$DEFAULT_PORT}"
    local service="${3:-$DEFAULT_SERVICE}"
    local method="${4:-$DEFAULT_METHOD}"
    
    info "Testing Oracle connection to $host:$port/$service"
    
    local password
    password=$(get_password "$method") || return 1
    
    local connection_string="sys/$password@$host:$port/$service as sysdba"
    
    # Test connection with a simple query
    local result
    result=$(echo "SELECT 'Connection successful' FROM dual;" | sqlplus -S "$connection_string" 2>&1)
    
    if echo "$result" | grep -q "Connection successful"; then
        info "Connection test successful"
        echo -e "${GREEN}✓ Connection successful${NC}"
        return 0
    else
        error "Connection test failed: $result"
    fi
}

# Execute SQL query
execute_query() {
    local query="$1"
    local host="${2:-$DEFAULT_HOST}"
    local port="${3:-$DEFAULT_PORT}"
    local service="${4:-$DEFAULT_SERVICE}"
    local method="${5:-$DEFAULT_METHOD}"
    
    info "Executing query on $host:$port/$service"
    
    local password
    password=$(get_password "$method") || return 1
    
    local connection_string="sys/$password@$host:$port/$service as sysdba"
    
    # Execute query
    echo "$query" | sqlplus -S "$connection_string"
}

# Show usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Oracle SYS Password Manager - Bash Version

OPTIONS:
    -m, --method METHOD     Password retrieval method (env|file|config|interactive|auto)
    -h, --host HOST         Oracle host (default: $DEFAULT_HOST)
    -p, --port PORT         Oracle port (default: $DEFAULT_PORT)
    -s, --service SERVICE   Oracle service name (default: $DEFAULT_SERVICE)
    -t, --test             Test connection only
    -q, --query QUERY      Execute SQL query
    -c, --config FILE      Configuration file path
    --help                 Show this help message

METHODS:
    env         - Get password from environment variable (ORACLE_SYS_PASSWORD)
    file        - Get password from text file
    config      - Get password from JSON configuration file
    interactive - Prompt user for password
    auto        - Try methods in order (default)

EXAMPLES:
    # Test connection with auto-detection
    $0 --test

    # Test with specific method
    $0 --test --method env

    # Execute query
    $0 --query "SELECT username FROM dba_users WHERE username='SYS'"

    # Custom connection parameters
    $0 --test --host myhost --port 1521 --service ORCL

EOF
}

# Main function
main() {
    local method="$DEFAULT_METHOD"
    local host="$DEFAULT_HOST"
    local port="$DEFAULT_PORT"
    local service="$DEFAULT_SERVICE"
    local test_only=false
    local query=""
    local config_file="$CONFIG_FILE"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--method)
                method="$2"
                shift 2
                ;;
            -h|--host)
                host="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -s|--service)
                service="$2"
                shift 2
                ;;
            -t|--test)
                test_only=true
                shift
                ;;
            -q|--query)
                query="$2"
                shift 2
                ;;
            -c|--config)
                config_file="$2"
                CONFIG_FILE="$config_file"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
    
    # Check dependencies
    check_dependencies
    
    if [[ "$test_only" == true ]]; then
        # Test connection
        test_connection "$host" "$port" "$service" "$method"
    elif [[ -n "$query" ]]; then
        # Execute query
        execute_query "$query" "$host" "$port" "$service" "$method"
    else
        # Just retrieve password
        local password
        password=$(get_password "$method")
        if [[ -n "$password" ]]; then
            info "Password retrieved successfully (length: ${#password})"
            echo -e "${GREEN}✓ Password retrieved successfully${NC}"
        else
            error "Failed to retrieve password"
        fi
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
