#!/usr/bin/env python3
"""
Oracle SYS Password Retrieval Script

This script provides secure methods to retrieve Oracle SYS user passwords
from various sources like environment variables, encrypted files, or configuration files.

Requirements:
- cx_Oracle or oracledb library for Oracle connectivity
- cryptography library for password encryption/decryption (optional)
"""

import os
import sys
import getpass
import json
import base64
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import oracledb  # Modern Oracle driver
    ORACLE_DRIVER = 'oracledb'
except ImportError:
    try:
        import cx_Oracle  # Legacy Oracle driver
        ORACLE_DRIVER = 'cx_Oracle'
        oracledb = cx_Oracle
    except ImportError:
        logger.error("Neither oracledb nor cx_Oracle is installed. Please install one of them.")
        sys.exit(1)

class OracleSysPasswordManager:
    """Manages Oracle SYS password retrieval from various sources."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or 'oracle_config.json'
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file if it exists."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load config file: {e}")
        return {}
    
    def get_password_from_env(self, env_var: str = 'ORACLE_SYS_PASSWORD') -> Optional[str]:
        """Retrieve password from environment variable."""
        password = os.getenv(env_var)
        if password:
            logger.info(f"Password retrieved from environment variable: {env_var}")
            return password
        else:
            logger.warning(f"Environment variable {env_var} not found")
            return None
    
    def get_password_from_file(self, file_path: str) -> Optional[str]:
        """Retrieve password from a text file."""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    password = f.read().strip()
                logger.info(f"Password retrieved from file: {file_path}")
                return password
            else:
                logger.warning(f"Password file not found: {file_path}")
                return None
        except Exception as e:
            logger.error(f"Error reading password file: {e}")
            return None
    
    def get_password_interactive(self) -> str:
        """Prompt user for password interactively."""
        logger.info("Prompting for password interactively")
        return getpass.getpass("Enter Oracle SYS password: ")
    
    def get_password_from_config(self, key: str = 'sys_password') -> Optional[str]:
        """Retrieve password from configuration file."""
        password = self.config.get(key)
        if password:
            logger.info(f"Password retrieved from config file: {self.config_file}")
            return password
        else:
            logger.warning(f"Password key '{key}' not found in config file")
            return None
    
    def get_password(self, method: str = 'auto') -> Optional[str]:
        """
        Retrieve password using specified method or auto-detection.
        
        Methods:
        - 'env': Environment variable
        - 'file': Text file
        - 'config': Configuration file
        - 'interactive': User prompt
        - 'auto': Try methods in order
        """
        if method == 'env':
            return self.get_password_from_env()
        elif method == 'file':
            file_path = self.config.get('password_file', 'oracle_sys_password.txt')
            return self.get_password_from_file(file_path)
        elif method == 'config':
            return self.get_password_from_config()
        elif method == 'interactive':
            return self.get_password_interactive()
        elif method == 'auto':
            # Try methods in order of preference
            password = self.get_password_from_env()
            if password:
                return password
            
            password = self.get_password_from_config()
            if password:
                return password
            
            file_path = self.config.get('password_file', 'oracle_sys_password.txt')
            password = self.get_password_from_file(file_path)
            if password:
                return password
            
            # Last resort: interactive prompt
            return self.get_password_interactive()
        else:
            logger.error(f"Unknown password retrieval method: {method}")
            return None

class OracleConnectionManager:
    """Manages Oracle database connections using SYS credentials."""
    
    def __init__(self, password_manager: OracleSysPasswordManager):
        self.password_manager = password_manager
    
    def create_connection_string(self, host: str, port: int, service_name: str) -> str:
        """Create Oracle connection string."""
        return f"{host}:{port}/{service_name}"
    
    def test_connection(self, host: str = 'localhost', port: int = 1521, 
                       service_name: str = 'XE', password_method: str = 'auto') -> bool:
        """Test Oracle SYS connection."""
        try:
            password = self.password_manager.get_password(password_method)
            if not password:
                logger.error("Failed to retrieve SYS password")
                return False
            
            connection_string = self.create_connection_string(host, port, service_name)
            logger.info(f"Testing connection to: {connection_string}")
            
            # Connect as SYSDBA
            connection = oracledb.connect(
                user='sys',
                password=password,
                dsn=connection_string,
                mode=oracledb.SYSDBA if ORACLE_DRIVER == 'oracledb' else oracledb.SYSDBA
            )
            
            # Test query
            cursor = connection.cursor()
            cursor.execute("SELECT 'Connection successful' FROM dual")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            logger.info(f"Connection test successful: {result[0]}")
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def execute_sys_query(self, query: str, host: str = 'localhost', 
                         port: int = 1521, service_name: str = 'XE',
                         password_method: str = 'auto') -> Optional[list]:
        """Execute a query as SYS user."""
        try:
            password = self.password_manager.get_password(password_method)
            if not password:
                logger.error("Failed to retrieve SYS password")
                return None
            
            connection_string = self.create_connection_string(host, port, service_name)
            
            connection = oracledb.connect(
                user='sys',
                password=password,
                dsn=connection_string,
                mode=oracledb.SYSDBA if ORACLE_DRIVER == 'oracledb' else oracledb.SYSDBA
            )
            
            cursor = connection.cursor()
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            return results
            
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return None

def main():
    """Main function to demonstrate usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Oracle SYS Password Manager')
    parser.add_argument('--method', choices=['env', 'file', 'config', 'interactive', 'auto'],
                       default='auto', help='Password retrieval method')
    parser.add_argument('--host', default='localhost', help='Oracle host')
    parser.add_argument('--port', type=int, default=1521, help='Oracle port')
    parser.add_argument('--service', default='XE', help='Oracle service name')
    parser.add_argument('--test', action='store_true', help='Test connection only')
    parser.add_argument('--query', help='Execute custom query')
    parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize managers
    password_manager = OracleSysPasswordManager(args.config)
    connection_manager = OracleConnectionManager(password_manager)
    
    if args.test:
        # Test connection
        success = connection_manager.test_connection(
            args.host, args.port, args.service, args.method
        )
        sys.exit(0 if success else 1)
    
    elif args.query:
        # Execute custom query
        results = connection_manager.execute_sys_query(
            args.query, args.host, args.port, args.service, args.method
        )
        if results:
            for row in results:
                print(row)
    
    else:
        # Just retrieve and display password (masked)
        password = password_manager.get_password(args.method)
        if password:
            print(f"Password retrieved successfully (length: {len(password)})")
        else:
            print("Failed to retrieve password")
            sys.exit(1)

if __name__ == '__main__':
    main()
